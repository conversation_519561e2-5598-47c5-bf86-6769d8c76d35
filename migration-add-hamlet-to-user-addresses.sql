-- Migration: Thêm trường hamlet vào bảng user_addresses
-- Ngày: 2025-01-04
-- <PERSON><PERSON> tả: Thêm trường hamlet (thôn/ấp/xóm/tổ) để hỗ trợ GHTK API

-- Thêm cột hamlet
ALTER TABLE user_addresses 
ADD COLUMN hamlet VARCHAR(100);

-- Thêm comment cho cột mới
COMMENT ON COLUMN user_addresses.hamlet IS 'Thôn/Ấp/Xóm/Tổ (dùng cho GHTK)';

-- Cập nhật dữ liệu hiện có với giá trị mặc định
UPDATE user_addresses 
SET hamlet = 'Khác' 
WHERE hamlet IS NULL;

-- Thêm constraint NOT NULL sau khi đã cập nhật dữ liệu
ALTER TABLE user_addresses 
ALTER COLUMN hamlet SET DEFAULT 'Khác';

-- Tạo index cho hamlet (optional, nếu cần search)
CREATE INDEX idx_user_addresses_hamlet 
ON user_addresses (hamlet);

-- Verify migration
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_addresses' 
AND column_name = 'hamlet';
