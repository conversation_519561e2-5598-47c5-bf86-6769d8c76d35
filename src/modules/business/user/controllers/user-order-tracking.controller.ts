import { <PERSON>, Get, Post, Param, Body, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiBody } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { UserOrderService } from '../services/user-order.service';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CalculateShippingFeeRequestDto, CalculateShippingFeeResponseDto } from '../dto/calculate-shipping-fee.dto';
import { TrackingApiResponseDto } from '../dto/tracking-response.dto';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';

/**
 * Controller xử lý tracking và webhook cho đơn hàng vận chuyển
 */
@ApiTags('User Orders - Tracking & Shipping')
@Controller('user/orders')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class UserOrderTrackingController {
  private readonly logger = new Logger(UserOrderTrackingController.name);

  constructor(
    private readonly userOrderService: UserOrderService,
  ) {}

  /**
   * Tracking trạng thái đơn hàng
   */
  @Get(':id/tracking')
  @ApiOperation({
    summary: 'Tracking trạng thái đơn hàng',
    description: 'Lấy thông tin tracking từ đơn vị vận chuyển và cập nhật trạng thái đơn hàng'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của đơn hàng',
    type: 'number',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin tracking đơn hàng',
    type: TrackingApiResponseDto
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
    BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
    BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED
  )
  async trackOrder(
    @Param('id') orderId: number,
    @CurrentUser() user: JwtPayload
  ) {
    try {
      this.logger.log(`User ${user.id} tracking đơn hàng ${orderId}`);

      const trackingInfo = await this.userOrderService.trackOrder(orderId, user.id);
      
      return {
        success: true,
        message: 'Lấy thông tin tracking thành công',
        data: trackingInfo
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tracking đơn hàng ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Tính phí vận chuyển trước khi đặt hàng
   */
  @Post('calculate-shipping-fee')
  @ApiOperation({
    summary: 'Tính phí vận chuyển',
    description: 'Tính phí vận chuyển cho đơn hàng trước khi đặt hàng'
  })
  @ApiBody({
    description: 'Thông tin để tính phí vận chuyển',
    type: CalculateShippingFeeRequestDto,
    examples: {
      'Sử dụng địa chỉ customer': {
        summary: 'Sử dụng địa chỉ của khách hàng',
        description: 'Không truyền deliveryAddress, hệ thống sẽ lấy địa chỉ từ customer',
        value: {
          shopId: 1,
          customerId: 18,
          products: [
            { productId: 60, quantity: 2 },
            { productId: 61, quantity: 1 }
          ],
          preferredCarrier: 'GHN'
        }
      },
      'Sử dụng địa chỉ cụ thể': {
        summary: 'Sử dụng địa chỉ giao hàng cụ thể',
        description: 'Truyền deliveryAddress để tính phí cho địa chỉ cụ thể',
        value: {
          shopId: 1,
          products: [
            { productId: 60, quantity: 2 },
            { productId: 61, quantity: 1 }
          ],
          deliveryAddress: '123 Nguyễn Văn Cừ, Phường Nguyễn Cư Trinh, Quận 1, Thành phố Hồ Chí Minh',
          preferredCarrier: 'GHN'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Phí vận chuyển đã tính',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Tính phí vận chuyển thành công' },
        data: {
          $ref: '#/components/schemas/CalculateShippingFeeResponseDto'
        }
      }
    }
  })
  async calculateShippingFee(
    @Body() calculateFeeDto: CalculateShippingFeeRequestDto,
    @CurrentUser() user: JwtPayload
  ) {
    try {
      this.logger.log(`User ${user.id} tính phí vận chuyển`);

      // Debug log để kiểm tra input
      this.logger.log('Calculate shipping fee input:', {
        userId: user.id,
        products: calculateFeeDto.products,
        deliveryAddress: calculateFeeDto.deliveryAddress,
        preferredCarrier: calculateFeeDto.preferredCarrier
      });

      // Sử dụng method public để tính phí vận chuyển với dữ liệu thực từ database
      const shippingResult = await this.userOrderService.calculateShippingFeeForProducts(
        user.id,
        calculateFeeDto.shopId,
        calculateFeeDto.products,
        calculateFeeDto.deliveryAddress,
        calculateFeeDto.preferredCarrier,
        calculateFeeDto.customerId
      );

      // Debug log để kiểm tra output
      this.logger.log('Calculate shipping fee result:', shippingResult);

      return {
        success: true,
        message: 'Tính phí vận chuyển thành công',
        data: shippingResult
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tính phí vận chuyển:`, error);
      throw error;
    }
  }


}

/**
 * Controller xử lý webhook từ các đơn vị vận chuyển
 */
@ApiTags('Shipping Webhooks')
@Controller('webhooks/shipping')
export class ShippingWebhookController {
  private readonly logger = new Logger(ShippingWebhookController.name);

  constructor(
    private readonly userOrderService: UserOrderService,
  ) {}

  /**
   * Webhook từ GHN
   */
  @Post('ghn')
  @ApiOperation({
    summary: 'Webhook từ GHN',
    description: 'Nhận cập nhật trạng thái đơn hàng từ GHN'
  })
  @ApiBody({
    description: 'Dữ liệu webhook từ GHN',
    schema: {
      type: 'object',
      properties: {
        Type: { type: 'string', example: 'switch_status' },
        OrderCode: { type: 'string', example: 'GHN123456789' },
        Status: { type: 'string', example: 'delivered' },
        Description: { type: 'string', example: 'Đã giao hàng thành công' },
        Time: { type: 'string', example: '2024-01-01 10:00:00' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook đã được xử lý thành công'
  })
  async handleGHNWebhook(@Body() webhookData: any) {
    try {
      this.logger.log('Nhận webhook từ GHN', webhookData);
      
      await this.userOrderService.handleShippingWebhook(webhookData, 'GHN');
      
      return {
        success: true,
        message: 'Webhook GHN đã được xử lý thành công'
      };
    } catch (error) {
      this.logger.error('Lỗi khi xử lý webhook GHN:', error);
      return {
        success: false,
        message: 'Lỗi khi xử lý webhook GHN'
      };
    }
  }

  /**
   * Webhook từ GHTK
   */
  @Post('ghtk')
  @ApiOperation({
    summary: 'Webhook từ GHTK',
    description: 'Nhận cập nhật trạng thái đơn hàng từ GHTK'
  })
  @ApiBody({
    description: 'Dữ liệu webhook từ GHTK',
    schema: {
      type: 'object',
      properties: {
        partner_id: { type: 'string', example: 'ORDER_123_1641708800000' },
        label_id: { type: 'string', example: 'GHTK123456789' },
        status_id: { type: 'number', example: 5 },
        action_time: { type: 'string', example: '2024-01-01 10:00:00' },
        reason_code: { type: 'string', example: '1' },
        reason: { type: 'string', example: 'Đã giao hàng thành công' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook đã được xử lý thành công'
  })
  async handleGHTKWebhook(@Body() webhookData: any) {
    try {
      this.logger.log('Nhận webhook từ GHTK', webhookData);
      
      await this.userOrderService.handleShippingWebhook(webhookData, 'GHTK');
      
      return {
        success: true,
        message: 'Webhook GHTK đã được xử lý thành công'
      };
    } catch (error) {
      this.logger.error('Lỗi khi xử lý webhook GHTK:', error);
      return {
        success: false,
        message: 'Lỗi khi xử lý webhook GHTK'
      };
    }
  }
}
