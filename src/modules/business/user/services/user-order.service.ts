import { Injectable, Logger } from '@nestjs/common';
import { UserOrderRepository, UserProductRepository, UserConvertCustomerRepository, UserAddressRepository } from '@modules/business/repositories';
import { PaginatedResult } from '@common/response';
import { QueryUserOrderDto, UserOrderListItemDto, UserOrderResponseDto, UserOrderStatusResponseDto, OrderStatusStatsDto, ShippingStatusStatsDto, UserConvertCustomerListItemDto, CreateUserOrderDto } from '../dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { OrderStatusEnum, ShippingStatusEnum, PaymentStatusEnum } from '../../enums';
import { UserOrder } from '../../entities';
import { Transactional } from 'typeorm-transactional';
import { GHTKShipmentService } from './ghtk-shipment.service';
import { GHNShipmentService } from './ghn-shipment.service';
import { UserShopInfoService } from './user-shop-info.service';
import { UserProviderShipmentService } from '@modules/integration/services/user-provider-shipment.service';
import { ProviderShipmentType } from '@modules/integration/constants/provider-shipment-type.enum';

/**
 * Service xử lý logic nghiệp vụ cho đơn hàng của người dùng
 */
@Injectable()
export class UserOrderService {
  private readonly logger = new Logger(UserOrderService.name);

  constructor(
    private readonly userOrderRepository: UserOrderRepository,
    private readonly userProductRepository: UserProductRepository,
    private readonly userConvertCustomerRepository: UserConvertCustomerRepository,
    private readonly userAddressRepository: UserAddressRepository,
    private readonly ghtkShipmentService: GHTKShipmentService,
    private readonly ghnShipmentService: GHNShipmentService,
    private readonly userShopInfoService: UserShopInfoService,
    private readonly userProviderShipmentService: UserProviderShipmentService,
  ) {}

  /**
   * Tạo đơn hàng mới
   * @param userId ID người dùng
   * @param createOrderDto Dữ liệu tạo đơn hàng
   * @returns Thông tin đơn hàng đã tạo
   */
  @Transactional()
  async createOrder(userId: number, createOrderDto: CreateUserOrderDto): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(`Tạo đơn hàng mới cho userId=${userId}`);

      // 1. Validate và lấy thông tin sản phẩm
      const productInfoData = await this.validateAndGetProductInfos(userId, createOrderDto.products);

      // 2. Validate thông tin khách hàng
      const customerId = createOrderDto.customerInfo.customerId;
      const existingCustomer = await this.userConvertCustomerRepository.findById(customerId);

      if (!existingCustomer || existingCustomer.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOMER_NOT_FOUND,
          'Khách hàng không tồn tại hoặc không thuộc về bạn'
        );
      }

      // 3. Xử lý thông tin logistics và tính phí vận chuyển
      const processedLogisticInfo = await this.processLogisticInfo(userId, createOrderDto.logisticInfo, existingCustomer);

      // 4. Validate shopId và lấy thông tin shop
      const shopInfo = await this.userShopInfoService.getShopById(createOrderDto.shopId, userId);
      if (!shopInfo) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          `Shop với ID ${createOrderDto.shopId} không tồn tại hoặc không thuộc về bạn`
        );
      }

      // 5. Xử lý thông tin vận chuyển và phí vận chuyển
      let shippingFee = 0;
      let selectedCarrier: string | null = null;
      let shippingServiceType: string | null = null;

      // Mặc định hasShipping = true nếu không được chỉ định
      const hasShipping = createOrderDto.hasShipping !== false;

      if (hasShipping && processedLogisticInfo) {
        // Sử dụng thông tin vận chuyển đã được tính toán trước (khuyến nghị)
        if (createOrderDto.billInfo.selectedCarrier && createOrderDto.billInfo.shippingFee !== undefined) {
          selectedCarrier = createOrderDto.billInfo.selectedCarrier;
          shippingFee = createOrderDto.billInfo.shippingFee;
          shippingServiceType = createOrderDto.billInfo.shippingServiceType || 'standard';

          this.logger.log(`Sử dụng thông tin vận chuyển đã tính toán: ${selectedCarrier}, phí: ${shippingFee} VND`);
        } else {
          // Fallback: Tính phí vận chuyển tự động (không khuyến nghị)
          this.logger.warn('Không có thông tin vận chuyển đã tính toán, đang tính toán tự động...');

          const shippingResult = await this.calculateAndSelectShipping(
            userId,
            createOrderDto.shopId,
            productInfoData,
            processedLogisticInfo,
            createOrderDto.logisticInfo?.carrier
          );

          shippingFee = shippingResult.fee;
          selectedCarrier = shippingResult.carrier;
          shippingServiceType = shippingResult.serviceType;

          this.logger.log(`Đã tính phí vận chuyển tự động: ${shippingFee} VND với ${selectedCarrier}`);
        }

        // Cập nhật logistic info với thông tin vận chuyển
        processedLogisticInfo.carrier = selectedCarrier;
        processedLogisticInfo.shippingFee = shippingFee;
        processedLogisticInfo.serviceType = shippingServiceType;
      }

      // 6. Cập nhật bill info với phí vận chuyển và tính toán tự động
      const calculatedTotal = (createOrderDto.billInfo.subtotal || 0) +
                             shippingFee +
                             (createOrderDto.billInfo.tax || 0) -
                             (createOrderDto.billInfo.discount || 0);

      const updatedBillInfo = {
        ...createOrderDto.billInfo,
        shippingFee,
        selectedCarrier,
        shippingServiceType,
        total: createOrderDto.billInfo.total || calculatedTotal, // Sử dụng total từ user hoặc tự động tính
        paymentStatus: createOrderDto.billInfo.paymentStatus || PaymentStatusEnum.PENDING,
      };

      // 7. Tạo đơn hàng với các giá trị mặc định
      const orderData: Partial<UserOrder> = {
        userId,
        userConvertCustomerId: customerId,
        productInfo: productInfoData,
        billInfo: updatedBillInfo,
        hasShipping,
        shippingStatus: createOrderDto.shippingStatus || ShippingStatusEnum.PENDING,
        logisticInfo: processedLogisticInfo,
        orderStatus: createOrderDto.orderStatus || OrderStatusEnum.PENDING,
        source: createOrderDto.source || 'website',
      };

      // Thêm thông tin bổ sung nếu có
      if (createOrderDto.note || createOrderDto.tags) {
        orderData.logisticInfo = {
          ...orderData.logisticInfo,
          note: createOrderDto.note,
          tags: createOrderDto.tags,
        };
      }

      const createdOrder = await this.userOrderRepository.createOrder(orderData);

      this.logger.log(`Đã tạo đơn hàng thành công với ID=${createdOrder.id}`);

      // 8. Submit đơn hàng đến đơn vị vận chuyển nếu có yêu cầu vận chuyển
      if (hasShipping && selectedCarrier && processedLogisticInfo) {
        try {
          await this.submitOrderToShippingProvider(createdOrder, selectedCarrier, processedLogisticInfo, productInfoData);
          this.logger.log(`Đã submit đơn hàng ${createdOrder.id} đến ${selectedCarrier} thành công`);
        } catch (shippingError) {
          this.logger.error(`Lỗi khi submit đơn hàng ${createdOrder.id} đến ${selectedCarrier}: ${shippingError.message}`);

          // Xóa đơn hàng đã tạo vì không thể submit đến shipping provider
          await this.userOrderRepository.delete({ id: createdOrder.id });
          this.logger.log(`Đã xóa đơn hàng ${createdOrder.id} do lỗi submit shipping`);

          // Throw error để báo lỗi cho client
          if (shippingError instanceof AppException) {
            throw shippingError;
          }
          throw new AppException(
            BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
            `Không thể tạo đơn hàng: ${shippingError.message}`
          );
        }
      }

      // 9. Trả về thông tin đơn hàng đã tạo
      return this.findById(createdOrder.id, userId);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Lỗi khi tạo đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Validate và lấy thông tin chi tiết sản phẩm
   * @param userId ID người dùng
   * @param products Danh sách sản phẩm trong đơn hàng
   * @returns Thông tin chi tiết sản phẩm
   */
  private async validateAndGetProductInfos(userId: number, products: any[]): Promise<Record<string, unknown>> {
    const productInfos: any[] = [];

    for (const productItem of products) {
      // Kiểm tra sản phẩm có tồn tại và thuộc về user không
      const product = await this.userProductRepository.findById(productItem.productId);
      if (!product || product.createdBy !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Sản phẩm với ID ${productItem.productId} không tồn tại hoặc không thuộc về bạn`
        );
      }

      // Tính toán giá và tổng tiền
      let unitPrice = 0;
      if (product.price && typeof product.price === 'object') {
        unitPrice = (product.price as any).salePrice || (product.price as any).listPrice || 0;
      }

      const totalPrice = unitPrice * productItem.quantity;

      // Lấy thông tin vận chuyển từ shipmentConfig
      const shipmentConfig = product.shipmentConfig || {};
      const weight = shipmentConfig.weightGram || 200; // Mặc định 200g nếu không có
      const dimensions = {
        length: shipmentConfig.lengthCm || 30,
        width: shipmentConfig.widthCm || 25,
        height: shipmentConfig.heightCm || 5
      };

      productInfos.push({
        productId: product.id,
        name: product.name,
        quantity: productItem.quantity,
        unitPrice,
        totalPrice,
        description: product.description,
        images: product.images,
        weight, // Trọng lượng từ shipmentConfig
        dimensions, // Kích thước từ shipmentConfig
        shipmentConfig: product.shipmentConfig, // Toàn bộ config để sử dụng sau này
      });
    }

    return { products: productInfos };
  }

  /**
   * Xử lý thông tin logistics và địa chỉ giao hàng
   * @param userId ID người dùng
   * @param logisticInfo Thông tin logistics từ request
   * @param customer Thông tin customer để lấy địa chỉ mặc định
   * @returns Thông tin logistics đã xử lý
   */
  private async processLogisticInfo(userId: number, logisticInfo: any, customer: any): Promise<any> {
    let deliveryAddressInfo = customer.address || ''; // Mặc định sử dụng địa chỉ customer
    let recipientName = customer.name || '';
    let recipientPhone = customer.phone || '';

    if (logisticInfo?.deliveryAddress) {
      // Nếu có thông tin địa chỉ trong logistic info
      if (logisticInfo.deliveryAddress.addressId) {
        // Trường hợp chọn địa chỉ có sẵn
        const existingAddress = await this.userAddressRepository.findByIdAndUserId(
          logisticInfo.deliveryAddress.addressId,
          userId
        );

        if (!existingAddress) {
          throw new AppException(
            BUSINESS_ERROR_CODES.ADDRESS_NOT_FOUND,
            'Địa chỉ không tồn tại hoặc không thuộc về bạn'
          );
        }

        deliveryAddressInfo = this.formatAddressString(existingAddress);
        recipientName = existingAddress.recipientName;
        recipientPhone = existingAddress.recipientPhone;
      } else if (logisticInfo.deliveryAddress.newAddress) {
        // Trường hợp tạo địa chỉ mới
        const newAddressData = {
          ...logisticInfo.deliveryAddress.newAddress,
          userId,
          isDefault: false, // Địa chỉ từ order không set làm mặc định
        };

        const createdAddress = await this.userAddressRepository.createAddress(newAddressData);
        deliveryAddressInfo = this.formatAddressString(createdAddress);
        recipientName = createdAddress.recipientName;
        recipientPhone = createdAddress.recipientPhone;
      }
    }

    return {
      shippingMethod: logisticInfo?.shippingMethod,
      carrier: logisticInfo?.carrier,
      shippingNote: logisticInfo?.shippingNote,
      deliveryAddress: deliveryAddressInfo,
      recipientName,
      recipientPhone,
    };
  }

  /**
   * Format địa chỉ thành chuỗi
   * @param address Thông tin địa chỉ
   * @returns Chuỗi địa chỉ đầy đủ
   */
  private formatAddressString(address: any): string {
    const parts = [
      address.address,
      address.ward,
      address.district,
      address.province,
    ].filter(Boolean);

    return parts.join(', ');
  }

  /**
   * Lấy danh sách đơn hàng của người dùng với phân trang
   * @param userId ID người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách đơn hàng với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserOrderDto): Promise<PaginatedResult<UserOrderListItemDto>> {
    try {
      this.logger.log(`Lấy danh sách đơn hàng cho userId=${userId}`);

      // Lấy danh sách đơn hàng từ repository
      const result = await this.userOrderRepository.findAll(userId, queryDto);

      // Chuyển đổi sang DTO response với thông tin customer đầy đủ
      const items = result.items.map(order => {
        const orderDto = plainToInstance(UserOrderListItemDto, order, { excludeExtraneousValues: true });

        // Map thông tin customer nếu có
        if (order.userConvertCustomer) {
          orderDto.userConvertCustomer = plainToInstance(
            UserConvertCustomerListItemDto,
            order.userConvertCustomer,
            { excludeExtraneousValues: true }
          );
        }

        return orderDto;
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách đơn hàng: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy danh sách đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID đơn hàng
   * @param userId ID người dùng
   * @returns Chi tiết đơn hàng
   */
  async findById(id: number, userId: number): Promise<UserOrderResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết đơn hàng id=${id} cho userId=${userId}`);

      // Lấy đơn hàng từ repository
      const order = await this.userOrderRepository.findById(id);

      // Kiểm tra đơn hàng tồn tại
      if (!order) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          `Không tìm thấy đơn hàng với ID ${id}`
        );
      }

      // Kiểm tra đơn hàng thuộc về người dùng
      if (order.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_ACCESS_DENIED,
          `Bạn không có quyền truy cập đơn hàng này`
        );
      }

      // Chuyển đổi sang DTO response
      return plainToInstance(UserOrderResponseDto, order, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy chi tiết đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Validate cấu hình provider của user
   * @param userId ID người dùng
   * @param carrier Đơn vị vận chuyển
   * @returns Promise<void>
   */
  private async validateUserProviderConfig(userId: number, carrier: string): Promise<void> {
    let providerType: ProviderShipmentType;

    if (carrier === 'GHN') {
      providerType = ProviderShipmentType.GHN;
    } else if (carrier === 'GHTK') {
      providerType = ProviderShipmentType.GHTK;
    } else {
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Đơn vị vận chuyển không được hỗ trợ: ${carrier}`
      );
    }

    // Kiểm tra user đã cấu hình provider chưa
    const config = await this.userProviderShipmentService.getDecryptedConfig(userId, providerType);

    if (!config) {
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Bạn chưa cấu hình đơn vị vận chuyển ${carrier}. Vui lòng cấu hình trong phần quản lý nhà cung cấp vận chuyển trước khi sử dụng.`
      );
    }

    // Validate token và thông tin cần thiết
    if (carrier === 'GHN' && (!config.token || !config.shopId)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Cấu hình ${carrier} không đầy đủ. Vui lòng kiểm tra lại token và shop ID.`
      );
    }

    if (carrier === 'GHTK' && !config.token) {
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Cấu hình ${carrier} không đầy đủ. Vui lòng kiểm tra lại token.`
      );
    }
  }

  /**
   * Tính phí và chọn đơn vị vận chuyển
   * @param userId ID người dùng
   * @param shopId ID shop để lấy địa chỉ gửi
   * @param productInfo Thông tin sản phẩm
   * @param logisticInfo Thông tin logistics
   * @param preferredCarrier Đơn vị vận chuyển ưu tiên
   * @returns Thông tin vận chuyển đã chọn
   */
  private async calculateAndSelectShipping(
    userId: number,
    shopId: number,
    productInfo: any,
    logisticInfo: any,
    preferredCarrier?: string
  ): Promise<{ carrier: string; fee: number; serviceType: string }> {
    try {
      this.logger.log('Tính phí và chọn đơn vị vận chuyển', { preferredCarrier });

      // Tính tổng trọng lượng và giá trị đơn hàng
      const totalWeight = this.calculateTotalWeight(productInfo);
      const totalValue = this.calculateTotalValue(productInfo);

      // Định tuyến đơn vị vận chuyển theo rule
      const selectedCarrier = this.selectCarrierByRule(logisticInfo.deliveryAddress, preferredCarrier);
      this.logger.log(`Selected carrier: ${selectedCarrier}, Preferred carrier: ${preferredCarrier}`);

      // Validate cấu hình provider của user trước khi tính phí
      await this.validateUserProviderConfig(userId, selectedCarrier);

      let shippingFee = 0;
      let serviceType = 'standard';

      if (selectedCarrier === 'GHN') {
        try {
          const ghnFeeResult = await this.calculateGHNFee(userId, shopId, logisticInfo, totalWeight, totalValue, productInfo);
          shippingFee = ghnFeeResult.fee;
          serviceType = ghnFeeResult.serviceType;
        } catch (error) {
          this.logger.error(`Lỗi khi tính phí GHN: ${error.message}`, error);
          throw new AppException(
            BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
            `Không thể tính phí vận chuyển GHN: ${error.message}. Vui lòng thử lại hoặc chọn đơn vị vận chuyển khác.`
          );
        }
      } else if (selectedCarrier === 'GHTK') {
        try {
          const ghtkFeeResult = await this.calculateGHTKFee(userId, shopId, logisticInfo, totalWeight, totalValue);
          shippingFee = ghtkFeeResult.fee;
          serviceType = ghtkFeeResult.serviceType;
        } catch (error) {
          this.logger.error(`Lỗi khi tính phí GHTK: ${error.message}`, error);
          throw new AppException(
            BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
            `Không thể tính phí vận chuyển GHTK: ${error.message}. Vui lòng thử lại hoặc chọn đơn vị vận chuyển khác.`
          );
        }
      } else {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          `Đơn vị vận chuyển không được hỗ trợ: ${selectedCarrier}`
        );
      }

      this.logger.log(`Đã chọn ${selectedCarrier} với phí ${shippingFee}đ`);
      return { carrier: selectedCarrier, fee: shippingFee, serviceType };
    } catch (error) {
      this.logger.error('Lỗi khi tính phí vận chuyển:', error);
      // Ném lỗi thay vì trả về phí mặc định để người dùng biết có vấn đề
      throw error;
    }
  }

  /**
   * Chọn đơn vị vận chuyển theo lựa chọn của người dùng
   * @param address Địa chỉ giao hàng (không sử dụng cho logic chọn)
   * @param preferredCarrier Đơn vị vận chuyển người dùng chọn
   * @returns Đơn vị vận chuyển được chọn
   */
  private selectCarrierByRule(address: string, preferredCarrier?: string): string {
    // Người dùng phải chọn đơn vị vận chuyển, không tự động chọn
    if (preferredCarrier && ['GHN', 'GHTK'].includes(preferredCarrier)) {
      return preferredCarrier;
    }

    // Nếu người dùng không chọn, mặc định sử dụng GHN
    return 'GHN';
  }

  /**
   * Tính tổng trọng lượng sản phẩm
   * @param productInfo Thông tin sản phẩm
   * @returns Tổng trọng lượng (gram)
   */
  private calculateTotalWeight(productInfo: any): number {
    if (!productInfo?.products || !Array.isArray(productInfo.products)) {
      return 500; // Trọng lượng mặc định 500g
    }

    return productInfo.products.reduce((total: number, product: any) => {
      // Sử dụng trọng lượng thực từ shipmentConfig của sản phẩm
      const weight = product.weight || 200; // Trọng lượng mặc định 200g/sản phẩm (theo default của entity)
      const quantity = product.quantity || 1;
      return total + (weight * quantity);
    }, 0);
  }

  /**
   * Tính tổng giá trị đơn hàng
   * @param productInfo Thông tin sản phẩm
   * @returns Tổng giá trị
   */
  private calculateTotalValue(productInfo: any): number {
    if (!productInfo?.products || !Array.isArray(productInfo.products)) {
      return 0;
    }

    return productInfo.products.reduce((total: number, product: any) => {
      const price = product.unitPrice || 0;
      const quantity = product.quantity || 1;
      return total + (price * quantity);
    }, 0);
  }

  /**
   * Tính phí vận chuyển GHN
   * @param userId ID người dùng
   * @param shopId ID shop để lấy địa chỉ gửi
   * @param logisticInfo Thông tin logistics
   * @param weight Trọng lượng
   * @param value Giá trị đơn hàng
   * @param productInfo Thông tin sản phẩm để lấy kích thước
   * @returns Phí vận chuyển GHN
   */
  private async calculateGHNFee(
    userId: number,
    shopId: number,
    logisticInfo: any,
    weight: number,
    value: number,
    productInfo?: any
  ): Promise<{ fee: number; serviceType: string }> {
    try {
      // Parse địa chỉ để lấy thông tin tỉnh/quận
      const addressInfo = this.parseAddress(logisticInfo.deliveryAddress);

      // Tính kích thước tổng hợp từ các sản phẩm (lấy kích thước lớn nhất)
      let maxLength = 30, maxWidth = 25, maxHeight = 5;
      if (productInfo?.products && Array.isArray(productInfo.products)) {
        productInfo.products.forEach((product: any) => {
          if (product.dimensions) {
            maxLength = Math.max(maxLength, product.dimensions.length || 30);
            maxWidth = Math.max(maxWidth, product.dimensions.width || 25);
            maxHeight = Math.max(maxHeight, product.dimensions.height || 5);
          }
        });
      }

      // Lấy địa chỉ gửi từ thông tin shop theo shopId
      // Sử dụng mapping cứng cho GHN (có thể cải thiện sau)
      const fromDistrictId = 1442; // Quận 1, TP.HCM (mặc định)
      const toDistrictId = addressInfo.districtId || 1443; // Mặc định quận 3 nếu không có

      // Lấy service ID phù hợp
      let serviceId = 53320; // Mặc định
      try {
        const availableServices = await this.ghnShipmentService.getServices(fromDistrictId, toDistrictId);
        if (availableServices?.data && availableServices.data.length > 0) {
          serviceId = availableServices.data[0].service_id;
          this.logger.log('Sử dụng service ID từ API:', serviceId);
        }
      } catch (serviceError) {
        this.logger.warn('Không thể lấy services, sử dụng service mặc định:', serviceError.message);
      }

      // Lấy ward code từ thông tin shop theo shopId
      // Sử dụng mapping cứng cho GHN (có thể cải thiện sau)
      const fromWardCode = '21211'; // Phường Bến Nghé (mặc định)

      // Tạo request tính phí GHN
      const feeRequest = {
        shopId: parseInt(process.env.GHN_SHOP_ID || '0'),
        serviceId: serviceId,
        serviceTypeId: 2, // E-commerce
        fromDistrictId: fromDistrictId,
        fromWardCode: fromWardCode,
        toDistrictId: toDistrictId,
        toWardCode: addressInfo.wardCode || '21308', // Mặc định phường khác
        weight: Math.max(weight, 100), // Tối thiểu 100g
        length: maxLength,
        width: maxWidth,
        height: maxHeight,
        insuranceValue: Math.min(value, 5000000), // Tối đa 5 triệu
        codValue: 0
      };

      // Tối ưu hóa: chỉ log khi có lỗi, không log request/response để tăng tốc độ
      const result = await this.ghnShipmentService.calculateFee(feeRequest);

      return {
        fee: result.data.total || 30000,
        serviceType: 'standard'
      };
    } catch (error) {
      this.logger.warn(`Lỗi tính phí GHN: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tính phí vận chuyển GHTK
   * @param userId ID người dùng
   * @param shopId ID shop để lấy địa chỉ gửi
   * @param logisticInfo Thông tin logistics
   * @param weight Trọng lượng
   * @param value Giá trị đơn hàng
   * @returns Phí vận chuyển GHTK
   */
  private async calculateGHTKFee(
    userId: number,
    shopId: number,
    logisticInfo: any,
    weight: number,
    value: number
  ): Promise<{ fee: number; serviceType: string }> {
    try {
      // Parse địa chỉ để lấy thông tin tỉnh/quận
      const addressInfo = this.parseAddress(logisticInfo.deliveryAddress);

      // Lấy địa chỉ gửi từ thông tin shop theo shopId
      const pickProvince = await this.userShopInfoService.getShopProvinceByShopId(shopId, userId);
      const pickDistrict = await this.userShopInfoService.getShopDistrictByShopId(shopId, userId);
      const pickWard = await this.userShopInfoService.getShopWardByShopId(shopId, userId);

      // Tạo request tính phí GHTK
      const feeRequest = {
        pickProvince: pickProvince,
        pickDistrict: pickDistrict,
        pickWard: pickWard,
        province: addressInfo.province || 'Hồ Chí Minh',
        district: addressInfo.district || 'Quận 1',
        ward: addressInfo.ward || 'Phường Bến Nghé',
        weight: Math.max(weight, 100), // Tối thiểu 100g
        value: value,
        transport: 'road', // Đường bộ
        deliverOption: 'none'
      };

      // Tối ưu hóa: chỉ log khi có lỗi, không log request/response để tăng tốc độ
      const result = await this.ghtkShipmentService.calculateFee(feeRequest);

      return {
        fee: result.fee.fee || 25000,
        serviceType: result.fee.deliveryType || 'standard'
      };
    } catch (error) {
      this.logger.warn(`Lỗi tính phí GHTK: ${error.message}`);
      throw error;
    }
  }

  /**
   * Parse địa chỉ để lấy thông tin tỉnh/quận/phường
   * @param address Địa chỉ đầy đủ
   * @returns Thông tin địa chỉ đã parse
   */
  private parseAddress(address: string): {
    province?: string;
    district?: string;
    ward?: string;
    districtId?: number;
    wardCode?: string;
  } {
    if (!address) {
      return {};
    }

    const addressParts = address.split(',').map(part => part.trim());

    // Tìm tỉnh/thành phố
    let province = '';
    let district = '';
    let ward = '';

    for (const part of addressParts) {
      if (part.toLowerCase().includes('hồ chí minh') || part.toLowerCase().includes('tp.hcm')) {
        province = 'Hồ Chí Minh';
      } else if (part.toLowerCase().includes('hà nội')) {
        province = 'Hà Nội';
      } else if (part.toLowerCase().includes('đà nẵng')) {
        province = 'Đà Nẵng';
      } else if (part.toLowerCase().includes('quận') || part.toLowerCase().includes('huyện')) {
        district = part;
      } else if (part.toLowerCase().includes('phường') || part.toLowerCase().includes('xã')) {
        ward = part;
      }
    }

    // Mapping đơn giản cho district ID và ward code (có thể mở rộng)
    let districtId = 1442; // Quận 1, TP.HCM mặc định
    let wardCode = '21211'; // Phường Bến Nghé mặc định

    return {
      province: province || 'Hồ Chí Minh',
      district: district || 'Quận 1',
      ward: ward || 'Phường Bến Nghé',
      districtId,
      wardCode
    };
  }

  /**
   * Submit đơn hàng đến đơn vị vận chuyển
   * @param order Đơn hàng
   * @param carrier Đơn vị vận chuyển
   * @param logisticInfo Thông tin logistics
   * @param productInfo Thông tin sản phẩm
   */
  private async submitOrderToShippingProvider(
    order: UserOrder,
    carrier: string,
    logisticInfo: any,
    productInfo: any
  ): Promise<void> {
    try {
      this.logger.log(`Submit đơn hàng ${order.id} đến ${carrier}`);

      const addressInfo = this.parseAddress(logisticInfo.deliveryAddress);
      const totalWeight = this.calculateTotalWeight(productInfo);
      const totalValue = this.calculateTotalValue(productInfo);

      if (carrier === 'GHN') {
        await this.submitToGHN(order, logisticInfo, addressInfo, productInfo, totalWeight, totalValue);
      } else if (carrier === 'GHTK') {
        await this.submitToGHTK(order, logisticInfo, addressInfo, productInfo, totalWeight, totalValue);
      }

      this.logger.log(`Đã submit đơn hàng ${order.id} đến ${carrier} thành công`);
    } catch (error) {
      this.logger.error(`Lỗi khi submit đơn hàng ${order.id} đến ${carrier}:`, error);
      throw error;
    }
  }

  /**
   * Submit đơn hàng đến GHN
   */
  private async submitToGHN(
    order: UserOrder,
    logisticInfo: any,
    addressInfo: any,
    productInfo: any,
    totalWeight: number,
    totalValue: number
  ): Promise<void> {
    try {
      // Tạo request cho GHN
      const ghnRequest = {
        shopId: parseInt(process.env.GHN_SHOP_ID || '0'),
        clientOrderCode: `ORDER_${order.id}_${Date.now()}`,
        toName: logisticInfo.recipientName || 'Khách hàng',
        toPhone: logisticInfo.recipientPhone || '0123456789',
        toAddress: logisticInfo.deliveryAddress,
        toWardCode: addressInfo.wardCode || '21211',
        toDistrictId: addressInfo.districtId || 1442,
        fromName: await this.userShopInfoService.getShopName(order.userId),
        fromPhone: await this.userShopInfoService.getShopPhone(order.userId),
        fromAddress: await this.userShopInfoService.getShopAddress(order.userId),
        fromWardCode: '21211',
        fromDistrictId: 1442,
        fromWardName: await this.userShopInfoService.getShopWard(order.userId),
        fromDistrictName: await this.userShopInfoService.getShopDistrict(order.userId),
        fromProvinceName: await this.userShopInfoService.getShopProvince(order.userId),
        returnPhone: await this.userShopInfoService.getShopPhone(order.userId),
        returnAddress: await this.userShopInfoService.getShopAddress(order.userId),
        returnDistrictId: 1442,
        returnWardCode: '21211',
        codAmount: logisticInfo.codAmount || 0,
        content: `Đơn hàng #${order.id}`,
        weight: Math.max(totalWeight, 100),
        length: 20,
        width: 15,
        height: 10,
        serviceTypeId: 2, // E-commerce
        serviceId: 53320, // Dịch vụ tiêu chuẩn
        paymentTypeId: 1, // Shop trả phí
        requiredNote: 'KHONGCHOXEMHANG',
        note: logisticInfo.shippingNote || '',
        items: productInfo.products?.map((product: any) => ({
          name: product.name,
          code: product.productId?.toString(),
          quantity: product.quantity,
          price: product.unitPrice,
          weight: product.weight || 100
        })) || []
      };

      const result = await this.ghnShipmentService.createOrder(ghnRequest);

      // Cập nhật logistic info với tracking number
      await this.updateOrderLogisticInfo(order.id, {
        trackingNumber: result.data.order_code,
        label: result.data.order_code, // GHN sử dụng order_code làm label
        estimatedDeliveryTime: result.data.expected_delivery_time,
        shippingProvider: 'GHN',
        shippingStatus: 'submitted'
      });

      this.logger.log(`Đã tạo vận đơn GHN: ${result.data.order_code}`);
    } catch (error) {
      this.logger.error('Lỗi khi submit đến GHN:', error);
      throw error;
    }
  }

  /**
   * Submit đơn hàng đến GHTK
   */
  private async submitToGHTK(
    order: UserOrder,
    logisticInfo: any,
    addressInfo: any,
    productInfo: any,
    totalWeight: number,
    totalValue: number
  ): Promise<void> {
    try {
      // Tạo request cho GHTK
      const ghtkRequest = {
        products: productInfo.products?.map((product: any) => ({
          name: product.name,
          price: product.unitPrice,
          weight: product.weight || 100,
          quantity: product.quantity,
          productCode: product.productId?.toString()
        })) || [],
        order: {
          id: `ORDER_${order.id}_${Date.now()}`,
          pickName: await this.userShopInfoService.getShopName(order.userId),
          pickAddress: await this.userShopInfoService.getShopAddress(order.userId),
          pickProvince: await this.userShopInfoService.getShopProvince(order.userId),
          pickDistrict: await this.userShopInfoService.getShopDistrict(order.userId),
          pickWard: await this.userShopInfoService.getShopWard(order.userId),
          pickTel: await this.userShopInfoService.getShopPhone(order.userId),
          name: logisticInfo.recipientName || 'Khách hàng',
          address: logisticInfo.deliveryAddress,
          province: addressInfo.province || 'Hồ Chí Minh',
          district: addressInfo.district || 'Quận 1',
          ward: addressInfo.ward || 'Phường Bến Nghé',
          hamlet: addressInfo.hamlet || 'Khác', // Lấy từ database, fallback 'Khác'
          tel: logisticInfo.recipientPhone || '0123456789',
          note: logisticInfo.shippingNote || '',
          value: totalValue,
          transport: 'road',
          pickMoney: logisticInfo.codAmount || 0,
          isFreeship: '1',
          pickOption: 'cod',
          deliverOption: 'none'
        }
      };

      const result = await this.ghtkShipmentService.createOrder(ghtkRequest);

      // Cập nhật logistic info với tracking number
      await this.updateOrderLogisticInfo(order.id, {
        trackingNumber: result.order.trackingId,
        label: result.order.label,
        estimatedDeliveryTime: result.order.estimatedDeliverTime,
        shippingProvider: 'GHTK',
        shippingStatus: 'submitted'
      });

      this.logger.log(`Đã tạo vận đơn GHTK: ${result.order.trackingId}`);
    } catch (error) {
      this.logger.error('Lỗi khi submit đến GHTK:', error);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin logistics của đơn hàng
   * @param orderId ID đơn hàng
   * @param updateData Dữ liệu cập nhật
   */
  private async updateOrderLogisticInfo(orderId: number, updateData: any): Promise<void> {
    try {
      const order = await this.userOrderRepository.findById(orderId);
      if (!order) {
        throw new Error(`Không tìm thấy đơn hàng ${orderId}`);
      }

      const updatedLogisticInfo = {
        ...order.logisticInfo,
        ...updateData,
        updatedAt: Date.now()
      };

      await this.userOrderRepository.updateOrder(orderId, {
        logisticInfo: updatedLogisticInfo
      });

      this.logger.log(`Đã cập nhật logistic info cho đơn hàng ${orderId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật logistic info cho đơn hàng ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Lấy thống kê trạng thái đơn hàng và vận chuyển của người dùng
   * @param userId ID người dùng
   * @returns Thống kê trạng thái đơn hàng và vận chuyển
   */
  async getOrderStatusStats(userId: number): Promise<UserOrderStatusResponseDto> {
    try {
      this.logger.log(`Lấy thống kê trạng thái đơn hàng cho userId=${userId}`);

      // Lấy thống kê từ repository
      const stats = await this.userOrderRepository.getOrderStatusStats(userId);

      // Debug logs
      this.logger.log(`Raw stats from repository:`, {
        totalOrders: stats.totalOrders,
        ordersWithShipping: stats.ordersWithShipping,
        ordersWithoutShipping: stats.ordersWithoutShipping,
        orderStatus: stats.orderStatus,
        shippingStatus: stats.shippingStatus,
      });

      // Khởi tạo các giá trị mặc định cho order status (tất cả đơn hàng)
      const orderStatusStats: OrderStatusStatsDto = {
        pending: stats.orderStatus[OrderStatusEnum.PENDING] || 0,
        confirmed: stats.orderStatus[OrderStatusEnum.CONFIRMED] || 0,
        processing: stats.orderStatus[OrderStatusEnum.PROCESSING] || 0,
        completed: stats.orderStatus[OrderStatusEnum.COMPLETED] || 0,
        cancelled: stats.orderStatus[OrderStatusEnum.CANCELLED] || 0,
        total: stats.totalOrders, // Sử dụng tổng số đơn hàng thực tế
      };

      // Khởi tạo các giá trị mặc định cho shipping status (tất cả trạng thái từ enum đã làm sạch)
      const shippingStatusStats: ShippingStatusStatsDto = {
        pending: stats.shippingStatus[ShippingStatusEnum.PENDING] || 0,
        preparing: stats.shippingStatus[ShippingStatusEnum.PREPARING] || 0,
        shipped: stats.shippingStatus[ShippingStatusEnum.SHIPPED] || 0,
        inTransit: stats.shippingStatus[ShippingStatusEnum.IN_TRANSIT] || 0,
        sorting: stats.shippingStatus[ShippingStatusEnum.SORTING] || 0,
        delivered: stats.shippingStatus[ShippingStatusEnum.DELIVERED] || 0,
        deliveryFailed: stats.shippingStatus[ShippingStatusEnum.DELIVERY_FAILED] || 0,
        returning: stats.shippingStatus[ShippingStatusEnum.RETURNING] || 0,
        cancelled: stats.shippingStatus[ShippingStatusEnum.CANCELLED] || 0,
        total: stats.ordersWithShipping, // Chỉ đếm đơn hàng có vận chuyển
      };

      this.logger.log(`Final calculated stats for userId=${userId}:`, {
        orderStatus: orderStatusStats,
        shippingStatus: shippingStatusStats,
      });

      // Tạo response DTO
      const response: UserOrderStatusResponseDto = {
        orderStatus: orderStatusStats,
        shippingStatus: shippingStatusStats,
      };

      return plainToInstance(UserOrderStatusResponseDto, response, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Tracking trạng thái đơn hàng từ đơn vị vận chuyển
   * @param orderId ID đơn hàng
   * @param userId ID người dùng
   * @returns Thông tin tracking
   */
  async trackOrder(orderId: number, userId: number): Promise<any> {
    try {
      this.logger.log(`Tracking đơn hàng ${orderId} cho userId=${userId}`);

      // Lấy thông tin đơn hàng
      const order = await this.userOrderRepository.findById(orderId);
      if (!order || order.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_NOT_FOUND,
          'Không tìm thấy đơn hàng hoặc bạn không có quyền truy cập'
        );
      }

      const logisticInfo = order.logisticInfo as any;
      if (!logisticInfo?.trackingNumber || !logisticInfo?.shippingProvider) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
          'Đơn hàng chưa có thông tin vận chuyển'
        );
      }

      // Validate cấu hình provider của user trước khi tracking
      await this.validateUserProviderConfig(userId, logisticInfo.shippingProvider);

      let trackingResult;
      if (logisticInfo.shippingProvider === 'GHN') {
        trackingResult = await this.ghnShipmentService.getOrderInfo(logisticInfo.trackingNumber);
      } else if (logisticInfo.shippingProvider === 'GHTK') {
        trackingResult = await this.ghtkShipmentService.getOrderStatus(logisticInfo.trackingNumber);
      } else {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
          'Đơn vị vận chuyển không được hỗ trợ'
        );
      }

      // Cập nhật trạng thái nếu có thay đổi
      await this.updateShippingStatusFromTracking(order, trackingResult, logisticInfo.shippingProvider);

      return {
        orderId: order.id,
        trackingNumber: logisticInfo.trackingNumber,
        carrier: logisticInfo.shippingProvider,
        status: trackingResult.data || trackingResult.order,
        lastUpdated: Date.now()
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tracking đơn hàng ${orderId}:`, error);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_FIND_FAILED,
        `Lỗi khi tracking đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Cập nhật trạng thái vận chuyển từ kết quả tracking
   * @param order Đơn hàng
   * @param trackingResult Kết quả tracking
   * @param carrier Đơn vị vận chuyển
   */
  private async updateShippingStatusFromTracking(
    order: UserOrder,
    trackingResult: any,
    carrier: string
  ): Promise<void> {
    try {
      let newShippingStatus: ShippingStatusEnum | null = null;
      let newOrderStatus: OrderStatusEnum | null = null;

      if (carrier === 'GHN') {
        // Mapping trạng thái GHN sang hệ thống
        const ghnStatus = trackingResult.data?.status;
        switch (ghnStatus) {
          case 'ready_to_pick':
          case 'picking':
            newShippingStatus = ShippingStatusEnum.PREPARING;
            break;
          case 'picked':
          case 'storing':
          case 'transporting':
            newShippingStatus = ShippingStatusEnum.IN_TRANSIT;
            break;
          case 'sorting':
            newShippingStatus = ShippingStatusEnum.SORTING;
            break;
          case 'delivering':
          case 'money_collect_delivering':
            newShippingStatus = ShippingStatusEnum.IN_TRANSIT;
            break;
          case 'delivered':
            newShippingStatus = ShippingStatusEnum.DELIVERED;
            newOrderStatus = OrderStatusEnum.COMPLETED;
            break;
          case 'delivery_fail':
            newShippingStatus = ShippingStatusEnum.DELIVERY_FAILED;
            break;
          case 'waiting_to_return':
          case 'return':
            newShippingStatus = ShippingStatusEnum.RETURNING;
            break;
          case 'cancel':
            newShippingStatus = ShippingStatusEnum.CANCELLED;
            newOrderStatus = OrderStatusEnum.CANCELLED;
            break;
        }
      } else if (carrier === 'GHTK') {
        // Mapping trạng thái GHTK sang hệ thống
        const ghtkStatusId = trackingResult.order?.statusId;
        switch (ghtkStatusId) {
          case 1: // Chờ lấy hàng
          case 2: // Đã lấy hàng
            newShippingStatus = ShippingStatusEnum.PREPARING;
            break;
          case 3: // Đang vận chuyển
          case 4: // Đang giao hàng
            newShippingStatus = ShippingStatusEnum.IN_TRANSIT;
            break;
          case 5: // Đã giao hàng
            newShippingStatus = ShippingStatusEnum.DELIVERED;
            newOrderStatus = OrderStatusEnum.COMPLETED;
            break;
          case 6: // Giao hàng thất bại
            newShippingStatus = ShippingStatusEnum.DELIVERY_FAILED;
            break;
          case 7: // Đang hoàn hàng
          case 8: // Đã hoàn hàng
            newShippingStatus = ShippingStatusEnum.RETURNING;
            break;
          case 9: // Hủy đơn hàng
            newShippingStatus = ShippingStatusEnum.CANCELLED;
            newOrderStatus = OrderStatusEnum.CANCELLED;
            break;
        }
      }

      // Cập nhật trạng thái nếu có thay đổi
      if (newShippingStatus && newShippingStatus !== order.shippingStatus) {
        const updateData: any = { shippingStatus: newShippingStatus };
        if (newOrderStatus && newOrderStatus !== order.orderStatus) {
          updateData.orderStatus = newOrderStatus;
        }

        await this.userOrderRepository.updateOrder(order.id, updateData);
        this.logger.log(`Đã cập nhật trạng thái đơn hàng ${order.id}: ${newShippingStatus}`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trạng thái từ tracking:`, error);
    }
  }

  /**
   * Xử lý webhook từ đơn vị vận chuyển
   * @param webhookData Dữ liệu webhook
   * @param carrier Đơn vị vận chuyển
   */
  async handleShippingWebhook(webhookData: any, carrier: string): Promise<void> {
    try {
      this.logger.log(`Xử lý webhook từ ${carrier}`, webhookData);

      let trackingNumber: string;
      let orderId: number | null = null;

      if (carrier === 'GHN') {
        trackingNumber = webhookData.OrderCode;
        // Tìm đơn hàng theo tracking number
        const order = await this.findOrderByTrackingNumber(trackingNumber);
        if (order) {
          orderId = order.id;
          await this.updateShippingStatusFromTracking(order, { data: webhookData }, carrier);
        }
      } else if (carrier === 'GHTK') {
        trackingNumber = webhookData.label_id;
        // Tìm đơn hàng theo tracking number
        const order = await this.findOrderByTrackingNumber(trackingNumber);
        if (order) {
          orderId = order.id;
          await this.updateShippingStatusFromTracking(order, { order: webhookData }, carrier);
        }
      }

      this.logger.log(`Đã xử lý webhook ${carrier} cho đơn hàng ${orderId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý webhook ${carrier}:`, error);
    }
  }

  /**
   * Tìm đơn hàng theo tracking number
   * @param trackingNumber Mã vận đơn
   * @returns Đơn hàng
   */
  private async findOrderByTrackingNumber(trackingNumber: string): Promise<UserOrder | null> {
    try {
      // Tìm trong repository (cần implement method này trong repository)
      return await this.userOrderRepository.findByTrackingNumber(trackingNumber);
    } catch (error) {
      this.logger.error(`Lỗi khi tìm đơn hàng theo tracking number ${trackingNumber}:`, error);
      return null;
    }
  }

  /**
   * Tính phí vận chuyển cho danh sách sản phẩm (public method cho API)
   * @param userId ID người dùng
   * @param shopId ID shop để lấy địa chỉ gửi
   * @param products Danh sách sản phẩm với productId và quantity
   * @param deliveryAddress Thông tin địa chỉ giao hàng (optional)
   * @param preferredCarrier Đơn vị vận chuyển ưu tiên
   * @param customerId ID customer để lấy địa chỉ mặc định (nếu không có deliveryAddress)
   * @returns Thông tin phí vận chuyển
   */
  async calculateShippingFeeForProducts(
    userId: number,
    shopId: number,
    products: Array<{ productId: number; quantity: number }>,
    deliveryAddress?: any,
    preferredCarrier?: string,
    customerId?: number
  ): Promise<{ carrier: string; fee: number; serviceType: string; estimatedDeliveryTime?: string }> {
    try {
      this.logger.log(`Tính phí vận chuyển cho ${products.length} sản phẩm của userId=${userId}, shopId=${shopId}`);

      // Validate shopId thuộc về user
      const shopInfo = await this.userShopInfoService.getShopById(shopId, userId);
      if (!shopInfo) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          `Shop với ID ${shopId} không tồn tại hoặc không thuộc về bạn`
        );
      }

      // Xử lý địa chỉ giao hàng - sử dụng logic tương tự như tạo đơn hàng
      let customer: any = null;
      if (!deliveryAddress && customerId) {
        // Lấy thông tin customer nếu không có deliveryAddress
        const foundCustomer = await this.userConvertCustomerRepository.findById(customerId);
        if (!foundCustomer || foundCustomer.userId !== userId) {
          throw new AppException(
            BUSINESS_ERROR_CODES.CUSTOMER_NOT_FOUND,
            'Khách hàng không tồn tại hoặc không thuộc về bạn'
          );
        }
        customer = foundCustomer;
      } else if (!deliveryAddress && !customerId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
          'Cần cung cấp địa chỉ giao hàng hoặc ID khách hàng'
        );
      }

      // Validate và lấy thông tin chi tiết sản phẩm từ database
      const productInfoData = await this.validateAndGetProductInfos(userId, products);

      // Xử lý thông tin logistics (sử dụng lại logic từ tạo đơn hàng)
      const logisticInfo = { deliveryAddress };
      const processedLogisticInfo = await this.processLogisticInfo(userId, logisticInfo, customer || { address: '', name: '', phone: '' });

      const finalLogisticInfo = {
        deliveryAddress: processedLogisticInfo.deliveryAddress
      };

      // Sử dụng method private hiện có để tính phí với shopId
      const shippingResult = await this.calculateAndSelectShipping(
        userId,
        shopId,
        productInfoData,
        finalLogisticInfo,
        preferredCarrier
      );

      return {
        carrier: shippingResult.carrier,
        fee: shippingResult.fee,
        serviceType: shippingResult.serviceType,
        estimatedDeliveryTime: '2-3 ngày' // Có thể cải thiện bằng cách tính toán thực tế
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tính phí vận chuyển cho sản phẩm: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.ORDER_CREATE_FAILED,
        `Lỗi khi tính phí vận chuyển: ${error.message}`
      );
    }
  }


}
