import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  ValidateNested,
} from 'class-validator';

/**
 * DTO cho thông tin sản phẩm trong request tính phí vận chuyển
 */
export class ShippingProductDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 1
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm',
    example: 2
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  quantity: number;
}

/**
 * DTO cho request tính phí vận chuyển
 */
export class CalculateShippingFeeRequestDto {
  @ApiProperty({
    description: 'ID shop để lấy địa chỉ gửi',
    example: 1
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  shopId: number;

  @ApiProperty({
    description: 'Danh sách sản phẩm',
    type: [ShippingProductDto],
    example: [
      {
        productId: 1,
        quantity: 2
      },
      {
        productId: 2,
        quantity: 1
      }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ShippingProductDto)
  products: ShippingProductDto[];

  @ApiProperty({
    description: 'ID khách hàng để lấy địa chỉ mặc định (nếu không có deliveryAddress)',
    example: 18,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  customerId?: number;

  @ApiProperty({
    description: 'Địa chỉ giao hàng cụ thể (nếu không truyền sẽ sử dụng địa chỉ của customer)',
    example: '123 Nguyễn Văn Cừ, Phường Nguyễn Cư Trinh, Quận 1, Thành phố Hồ Chí Minh',
    required: false,
    examples: {
      'Quận 1 TP.HCM': {
        value: '123 Nguyễn Văn Cừ, Phường Nguyễn Cư Trinh, Quận 1, Thành phố Hồ Chí Minh',
        description: 'Địa chỉ trong Quận 1, TP.HCM'
      },
      'Quận 3 TP.HCM': {
        value: '456 Võ Văn Tần, Phường 6, Quận 3, Thành phố Hồ Chí Minh',
        description: 'Địa chỉ trong Quận 3, TP.HCM'
      },
      'Hà Nội': {
        value: '789 Đường Láng, Phường Láng Thượng, Quận Đống Đa, Hà Nội',
        description: 'Địa chỉ tại Hà Nội'
      },
      'Đà Nẵng': {
        value: '321 Lê Duẩn, Phường Hải Châu I, Quận Hải Châu, Đà Nẵng',
        description: 'Địa chỉ tại Đà Nẵng'
      },
      'Cần Thơ': {
        value: '654 Trần Hưng Đạo, Phường An Nghiệp, Quận Ninh Kiều, Cần Thơ',
        description: 'Địa chỉ tại Cần Thơ'
      }
    }
  })
  @IsOptional()
  @IsString()
  deliveryAddress?: string;

  @ApiProperty({
    description: 'Đơn vị vận chuyển ưu tiên',
    enum: ['GHN', 'GHTK'],
    example: 'GHN',
    examples: {
      'GHN': {
        value: 'GHN',
        description: 'Giao Hàng Nhanh - phù hợp cho nội thành và liên tỉnh'
      },
      'GHTK': {
        value: 'GHTK',
        description: 'Giao Hàng Tiết Kiệm - phù hợp cho các tỉnh xa'
      }
    }
  })
  @IsNotEmpty()
  @IsEnum(['GHN', 'GHTK'])
  preferredCarrier: 'GHN' | 'GHTK';
}

/**
 * DTO cho response tính phí vận chuyển
 */
export class CalculateShippingFeeResponseDto {
  @ApiProperty({
    description: 'Đơn vị vận chuyển được chọn',
    example: 'GHN'
  })
  carrier: string;

  @ApiProperty({
    description: 'Phí vận chuyển (VND)',
    example: 30000
  })
  fee: number;

  @ApiProperty({
    description: 'Loại dịch vụ',
    example: 'standard'
  })
  serviceType: string;

  @ApiProperty({
    description: 'Thời gian giao hàng dự kiến',
    example: '2-3 ngày',
    required: false
  })
  estimatedDeliveryTime?: string;
}
