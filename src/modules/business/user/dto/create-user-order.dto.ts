import { ApiProperty, ApiExtraModels } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  Min,
  ValidateNested
} from 'class-validator';
import { PaymentMethodEnum, PaymentStatusEnum, ShippingStatusEnum, OrderStatusEnum, ShippingMethodEnum } from '../../enums';
import { OrderAddressDto } from './user-address.dto';

/**
 * DTO cho thông tin sản phẩm trong đơn hàng
 */
export class OrderProductItemDto {
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID sản phẩm không được để trống' })
  @IsNumber({}, { message: 'ID sản phẩm phải là số' })
  @Min(1, { message: 'ID sản phẩm phải lớn hơn 0' })
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm',
    example: 2,
  })
  @IsNotEmpty({ message: 'Số lượng không được để trống' })
  @IsNumber({}, { message: 'Số lượng phải là số' })
  @Min(1, { message: 'Số lượng phải lớn hơn 0' })
  quantity: number;
}

/**
 * DTO cho thông tin khách hàng trong đơn hàng
 */
export class OrderCustomerInfoDto {
  @ApiProperty({
    description: 'ID khách hàng chuyển đổi',
    example: 9,
  })
  @IsNotEmpty({ message: 'ID khách hàng không được để trống' })
  @IsNumber({}, { message: 'ID khách hàng phải là số' })
  @Min(1, { message: 'ID khách hàng phải lớn hơn 0' })
  customerId: number;
}

/**
 * DTO cho thông tin hóa đơn
 */
export class OrderBillInfoDto {
  @ApiProperty({
    description: 'Tổng tiền hàng',
    example: 300000,
  })
  @IsNotEmpty({ message: 'Tổng tiền hàng không được để trống' })
  @IsNumber({}, { message: 'Tổng tiền hàng phải là số' })
  @Min(0, { message: 'Tổng tiền hàng phải lớn hơn hoặc bằng 0' })
  subtotal: number;

  @ApiProperty({
    description: 'Thuế',
    example: 30000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Thuế phải là số' })
  @Min(0, { message: 'Thuế phải lớn hơn hoặc bằng 0' })
  tax?: number;

  @ApiProperty({
    description: 'Phí vận chuyển (từ kết quả tính phí vận chuyển)',
    example: 20000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Phí vận chuyển phải là số' })
  @Min(0, { message: 'Phí vận chuyển phải lớn hơn hoặc bằng 0' })
  shippingFee?: number;

  @ApiProperty({
    description: 'Đơn vị vận chuyển được chọn (từ kết quả tính phí vận chuyển)',
    example: 'GHN',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Đơn vị vận chuyển phải là chuỗi' })
  selectedCarrier?: string;

  @ApiProperty({
    description: 'Loại dịch vụ vận chuyển (từ kết quả tính phí vận chuyển)',
    example: 'standard',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Loại dịch vụ vận chuyển phải là chuỗi' })
  shippingServiceType?: string;

  @ApiProperty({
    description: 'Giảm giá',
    example: 10000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Giảm giá phải là số' })
  @Min(0, { message: 'Giảm giá phải lớn hơn hoặc bằng 0' })
  discount?: number;

  @ApiProperty({
    description: 'Tổng tiền thanh toán (tự động tính toán nếu không cung cấp)',
    example: 340000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Tổng tiền thanh toán phải là số' })
  @Min(0, { message: 'Tổng tiền thanh toán phải lớn hơn hoặc bằng 0' })
  total?: number;

  @ApiProperty({
    description: 'Phương thức thanh toán',
    enum: PaymentMethodEnum,
    example: PaymentMethodEnum.CASH,
  })
  @IsNotEmpty({ message: 'Phương thức thanh toán không được để trống' })
  @IsEnum(PaymentMethodEnum, { message: 'Phương thức thanh toán không hợp lệ' })
  paymentMethod: PaymentMethodEnum;

  @ApiProperty({
    description: 'Trạng thái thanh toán (mặc định: PENDING)',
    enum: PaymentStatusEnum,
    example: PaymentStatusEnum.PENDING,
    required: false,
    default: PaymentStatusEnum.PENDING,
  })
  @IsOptional()
  @IsEnum(PaymentStatusEnum, { message: 'Trạng thái thanh toán không hợp lệ' })
  paymentStatus?: PaymentStatusEnum;

  @ApiProperty({
    description: 'Số tiền thu hộ (COD)',
    example: 340000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số tiền thu hộ phải là số' })
  @Min(0, { message: 'Số tiền thu hộ phải lớn hơn hoặc bằng 0' })
  codAmount?: number;
}

/**
 * DTO cho thông tin vận chuyển
 */
export class OrderLogisticInfoDto {
  @ApiProperty({
    description: 'Phương thức vận chuyển',
    enum: ShippingMethodEnum,
    example: ShippingMethodEnum.GHN_STANDARD,
    examples: {
      'GHN Chuẩn': {
        value: ShippingMethodEnum.GHN_STANDARD,
        description: 'GHN Dịch vụ chuẩn - E-commerce (2-3 ngày)'
      },
      'GHN Nhanh': {
        value: ShippingMethodEnum.GHN_EXPRESS,
        description: 'GHN Dịch vụ nhanh - Express (1-2 ngày)'
      },
      'GHN Hỏa tốc': {
        value: ShippingMethodEnum.GHN_URGENT,
        description: 'GHN Dịch vụ hỏa tốc - Urgent (trong 24h)'
      },
      'GHTK Đường bộ': {
        value: ShippingMethodEnum.GHTK_ROAD,
        description: 'GHTK Vận chuyển đường bộ (3-5 ngày)'
      },
      'GHTK Đường hàng không': {
        value: ShippingMethodEnum.GHTK_FLY,
        description: 'GHTK Vận chuyển đường hàng không (1-2 ngày)'
      }
    }
  })
  @IsNotEmpty({ message: 'Phương thức vận chuyển không được để trống' })
  @IsEnum(ShippingMethodEnum, { message: 'Phương thức vận chuyển không hợp lệ' })
  shippingMethod: ShippingMethodEnum;

  @ApiProperty({
    description: 'Đơn vị vận chuyển',
    example: 'GHN',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Đơn vị vận chuyển phải là chuỗi' })
  @MaxLength(100, { message: 'Đơn vị vận chuyển không được vượt quá 100 ký tự' })
  carrier?: string;

  @ApiProperty({
    description: 'Ghi chú vận chuyển',
    example: 'Giao hàng trong giờ hành chính',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Ghi chú vận chuyển phải là chuỗi' })
  @MaxLength(1000, { message: 'Ghi chú vận chuyển không được vượt quá 1000 ký tự' })
  shippingNote?: string;

  @ApiProperty({
    description: 'Thông tin địa chỉ giao hàng (chọn có sẵn hoặc tạo mới). Nếu không truyền sẽ sử dụng địa chỉ và số điện thoại của customer.',
    type: OrderAddressDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => OrderAddressDto)
  deliveryAddress?: OrderAddressDto;
}

/**
 * DTO chính cho việc tạo đơn hàng mới
 */
@ApiExtraModels()
export class CreateUserOrderDto {
  @ApiProperty({
    description: 'ID shop để xác định địa chỉ gửi hàng',
    example: 1,
    examples: {
      'Tối thiểu': {
        summary: 'Request tối thiểu - chỉ cần thiết',
        description: 'Ví dụ request ngắn gọn với các trường cần thiết',
        value: {
          shopId: 1,
          customerInfo: { customerId: 18 },
          products: [{ productId: 60, quantity: 2 }],
          billInfo: {
            subtotal: 300000,
            shippingFee: 20000,
            selectedCarrier: "GHN",
            shippingServiceType: "standard",
            discount: 10000,
            paymentMethod: "CASH"
          },
          logisticInfo: {
            shippingMethod: "Chuẩn",
            carrier: "GHN",
            shippingNote: "Giao hàng trong giờ hành chính"
          },
          source: "website",
          note: "Đơn hàng ưu tiên",
          tags: ["urgent", "vip"]
        }
      },
      'Sử dụng GHN': {
        summary: 'Đơn hàng sử dụng Giao Hàng Nhanh (GHN)',
        description: 'Ví dụ tạo đơn hàng với nhà vận chuyển GHN',
        value: {
          shopId: 1,
          customerInfo: { customerId: 19 },
          products: [{ productId: 60, quantity: 2 }],
          billInfo: {
            subtotal: 300000,
            shippingFee: 31500,
            selectedCarrier: "GHN",
            shippingServiceType: "standard",
            discount: 10000,
            paymentMethod: "CASH"
          },
          logisticInfo: {
            shippingMethod: "Chuẩn",
            carrier: "GHN",
            shippingNote: "Giao hàng trong giờ hành chính",
            deliveryAddress: {
              addressId: 1
            }
          },
          source: "website",
          note: "Đơn hàng ưu tiên - GHN",
          tags: ["urgent", "ghn"]
        }
      },
      'Sử dụng GHTK': {
        summary: 'Đơn hàng sử dụng Giao Hàng Tiết Kiệm (GHTK)',
        description: 'Ví dụ tạo đơn hàng với nhà vận chuyển GHTK',
        value: {
          shopId: 1,
          customerInfo: { customerId: 19 },
          products: [{ productId: 60, quantity: 2 }],
          billInfo: {
            subtotal: 300000,
            shippingFee: 21500,
            selectedCarrier: "GHTK",
            shippingServiceType: "standard",
            discount: 10000,
            paymentMethod: "CASH"
          },
          logisticInfo: {
            shippingMethod: "Chuẩn",
            carrier: "GHTK",
            shippingNote: "Giao hàng trong giờ hành chính",
            deliveryAddress: {
              addressId: 1
            }
          },
          source: "website",
          note: "Đơn hàng ưu tiên - GHTK",
          tags: ["urgent", "ghtk"]
        }
      },
      'Đầy đủ': {
        summary: 'Request đầy đủ với tất cả tùy chọn',
        description: 'Ví dụ với đầy đủ các trường tùy chọn',
        value: {
          shopId: 1,
          customerInfo: { customerId: 18 },
          products: [
            { productId: 60, quantity: 2 },
            { productId: 61, quantity: 1 }
          ],
          billInfo: {
            subtotal: 300000,
            tax: 30000,
            shippingFee: 20000,
            selectedCarrier: "GHN",
            shippingServiceType: "standard",
            discount: 10000,
            total: 340000,
            paymentMethod: "CASH",
            paymentStatus: "PENDING",
            codAmount: 340000
          },
          hasShipping: true,
          logisticInfo: {
            shippingMethod: "Chuẩn",
            carrier: "GHN",
            shippingNote: "Giao hàng trong giờ hành chính",
            deliveryAddress: {
              newAddress: {
                recipientName: "Nguyễn Văn A",
                recipientPhone: "0912345678",
                address: "123 Đường ABC, Phường 1",
                province: "TP. Hồ Chí Minh",
                district: "Quận 1",
                ward: "Phường Bến Nghé",
                postalCode: "70000",
                isDefault: false,
                addressType: "home",
                note: "Gần chợ Bến Thành"
              }
            }
          },
          shippingStatus: "PENDING",
          orderStatus: "PENDING",
          source: "website",
          note: "Đơn hàng ưu tiên",
          tags: ["urgent", "vip"]
        }
      }
    }
  })
  @IsNotEmpty({ message: 'ID shop không được để trống' })
  @IsNumber({}, { message: 'ID shop phải là số' })
  @Min(1, { message: 'ID shop phải lớn hơn 0' })
  shopId: number;

  @ApiProperty({
    description: 'Thông tin khách hàng',
    type: OrderCustomerInfoDto,
  })
  @IsNotEmpty({ message: 'Thông tin khách hàng không được để trống' })
  @ValidateNested()
  @Type(() => OrderCustomerInfoDto)
  customerInfo: OrderCustomerInfoDto;

  @ApiProperty({
    description: 'Danh sách sản phẩm',
    type: [OrderProductItemDto],
    example: [
      { productId: 1, quantity: 2 },
      { productId: 2, quantity: 1 },
    ],
  })
  @IsNotEmpty({ message: 'Danh sách sản phẩm không được để trống' })
  @IsArray({ message: 'Danh sách sản phẩm phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => OrderProductItemDto)
  products: OrderProductItemDto[];

  @ApiProperty({
    description: 'Thông tin hóa đơn',
    type: OrderBillInfoDto,
  })
  @IsNotEmpty({ message: 'Thông tin hóa đơn không được để trống' })
  @ValidateNested()
  @Type(() => OrderBillInfoDto)
  billInfo: OrderBillInfoDto;

  @ApiProperty({
    description: 'Đơn hàng có yêu cầu vận chuyển hay không (mặc định: true)',
    example: true,
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường hasShipping phải là boolean' })
  hasShipping?: boolean;

  @ApiProperty({
    description: 'Thông tin vận chuyển',
    type: OrderLogisticInfoDto,
    required: false,
    examples: {
      'Sử dụng địa chỉ customer': {
        summary: 'Sử dụng địa chỉ và số điện thoại của customer',
        description: 'Không truyền deliveryAddress, hệ thống sẽ tự động sử dụng thông tin từ customer',
        value: {
          shippingMethod: "Chuẩn",
          carrier: "GHN",
          shippingNote: "Giao hàng trong giờ hành chính"
        }
      },
      'Sử dụng địa chỉ có sẵn': {
        summary: 'Chọn địa chỉ đã lưu trong hệ thống',
        description: 'Sử dụng địa chỉ đã được lưu trước đó',
        value: {
          shippingMethod: "Chuẩn",
          carrier: "GHN",
          shippingNote: "Giao hàng trong giờ hành chính",
          deliveryAddress: {
            addressId: 1
          }
        }
      },
      'Tạo địa chỉ mới': {
        summary: 'Tạo địa chỉ mới cho đơn hàng',
        description: 'Tạo địa chỉ mới và lưu vào hệ thống',
        value: {
          shippingMethod: "Chuẩn",
          carrier: "GHN",
          shippingNote: "Giao hàng trong giờ hành chính",
          deliveryAddress: {
            newAddress: {
              recipientName: "Nguyễn Văn A",
              recipientPhone: "0912345678",
              address: "123 Đường ABC, Phường 1",
              province: "TP. Hồ Chí Minh",
              district: "Quận 1",
              ward: "Phường Bến Nghé",
              postalCode: "70000",
              isDefault: false,
              addressType: "home",
              note: "Gần chợ Bến Thành"
            }
          }
        }
      }
    }
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => OrderLogisticInfoDto)
  logisticInfo?: OrderLogisticInfoDto;

  @ApiProperty({
    description: 'Trạng thái vận chuyển',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.PENDING,
    required: false,
  })
  @IsOptional()
  @IsEnum(ShippingStatusEnum, { message: 'Trạng thái vận chuyển không hợp lệ' })
  shippingStatus?: ShippingStatusEnum;

  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    enum: OrderStatusEnum,
    example: OrderStatusEnum.PENDING,
    required: false,
  })
  @IsOptional()
  @IsEnum(OrderStatusEnum, { message: 'Trạng thái đơn hàng không hợp lệ' })
  orderStatus?: OrderStatusEnum;

  @ApiProperty({
    description: 'Nguồn đơn hàng',
    example: 'website',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nguồn đơn hàng phải là chuỗi' })
  @MaxLength(45, { message: 'Nguồn đơn hàng không được vượt quá 45 ký tự' })
  source?: string;

  @ApiProperty({
    description: 'Ghi chú đơn hàng',
    example: 'Đơn hàng ưu tiên',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Ghi chú đơn hàng phải là chuỗi' })
  @MaxLength(1000, { message: 'Ghi chú đơn hàng không được vượt quá 1000 ký tự' })
  note?: string;

  @ApiProperty({
    description: 'Nhãn đơn hàng',
    example: ['urgent', 'vip'],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Nhãn đơn hàng phải là mảng' })
  @IsString({ each: true, message: 'Mỗi nhãn phải là chuỗi' })
  tags?: string[];
}
